"""
Comprehensive unit tests for the FastAPI app endpoints.

This module contains comprehensive tests for all FastAPI endpoints in the AI Analyst application,
including basic endpoints, analysis endpoints, SEC endpoints, document endpoints, and text processing endpoints.
"""
import os
import sys
from datetime import datetime
from unittest.mock import patch, Mock

from fastapi.testclient import TestClient

from app.model import DocumentContent

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Mock problematic imports to avoid dependency issues during testing
sys.modules['app.AI_Controller'] = Mock()
sys.modules['app.SEC_Controller'] = Mock()

from app.app import app


class TestBasicEndpoints:
    """Test class for basic FastAPI app endpoints."""

    def setup_method(self):
        """Set up test client before each test."""
        self.client = TestClient(app)

    def test_root_endpoint(self):
        """Test the root endpoint returns correct response."""
        response = self.client.get("/")

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "OK"
        assert data["message"] == "Python FastAPi OK"
        assert data["data"] is None

    def test_health_check_endpoint(self):
        """Test the health check endpoint returns healthy status."""
        response = self.client.get("/health")

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "OK"
        assert data["message"] == "Python FastAPi Healthy"
        assert data["data"] is None

    def test_root_endpoint_response_structure(self):
        """Test that root endpoint response has correct structure."""
        response = self.client.get("/")

        assert response.status_code == 200
        data = response.json()

        # Verify all required fields are present
        required_fields = ["status", "message", "data"]
        for field in required_fields:
            assert field in data

        # Verify field types
        assert isinstance(data["status"], str)
        assert isinstance(data["message"], str)

    def test_health_endpoint_response_structure(self):
        """Test that health endpoint response has correct structure."""
        response = self.client.get("/health")

        assert response.status_code == 200
        data = response.json()

        # Verify all required fields are present
        required_fields = ["status", "message", "data"]
        for field in required_fields:
            assert field in data

        # Verify field types
        assert isinstance(data["status"], str)
        assert isinstance(data["message"], str)


class TestDocumentUploadEndpoints:
    """Test class for document upload endpoints."""

    def setup_method(self):
        """Set up test client before each test."""
        self.client = TestClient(app)

    @patch('app.AI_Controller.upload_document')
    def test_upload_document_success(self, mock_upload):
        """Test successful document upload."""
        from app.model import UploadFileResponse, UploadFileRequest, DocumentType

        # Mock the upload_document function
        mock_request = UploadFileRequest(
            model="openai:gpt-4",
            filename="filename.txt",
            document=DocumentContent(
                type=DocumentType.TEN_K,
                content="Sample document content",
                title="Sample Document",
                filing_date="2023-12-31",
                report_date="2023-12-31",
                ticker="AAPL"
            )
        )

        mock_upload.return_value = UploadFileResponse(
            owner="system",
            request=mock_request,
            status="OK",
            message="Document uploaded successfully"
        )

        request_data = {
            "owner": "test_user",
            "model": "openai:gpt-4",
            "filename": "filename.txt",
            "document": {
                "type": "10-k",
                "cik": 320193,
                "ticker": "AAPL",
                "filing_date": "2023-11-03",
                "report_date": "2023-09-30",
                "title": "Apple Inc. 10-K",
                "content": "Sample 10-K content for testing"
            }
        }

        response = self.client.post("/document/upload", json=request_data)

        assert response.status_code == 200
        mock_upload.assert_called_once()

        # Verify the call was made with correct owner
        call_args = mock_upload.call_args
        assert call_args[0][0] == "system"  # owner parameter

    @patch('app.AI_Controller.upload_document')
    def test_upload_document_minimal_data(self, mock_upload):
        """Test document upload with minimal required data."""
        from app.model import UploadFileResponse, UploadFileRequest, DocumentType

        mock_request = UploadFileRequest(
            owner="test_user",
            model="openai:gpt-4",
            filename="filename.txt",
            document=DocumentContent(
                type=DocumentType.TEN_K,
                content="Minimal document content",
                ticker="AAPL"
            )
        )

        mock_upload.return_value = UploadFileResponse(
            owner="system",
            request=mock_request,
            status="OK"
        )

        request_data = {
            "owner": "test_user",
            "model": "openai:gpt-4",
            "filename": "filename.txt",
            "document": {
                "type": "10-k",
                "cik": 320193,
                "ticker": "AAPL",
                "filing_date": "2023-11-03",
                "report_date": "2023-09-30",
                "title": "Apple Inc. 10-K",
                "content": "Sample 10-K content for testing"
            }
        }

        response = self.client.post("/document/upload", json=request_data)

        assert response.status_code == 200
        mock_upload.assert_called_once()

    def test_upload_document_invalid_data(self):
        """Test document upload with invalid data."""
        # Missing required fields
        request_data = {
            "model": "openai:gpt-4"
            # Missing document
        }

        response = self.client.post("/document/upload", json=request_data)

        assert response.status_code == 422  # Validation error

    @patch('app.AI_Controller.upload_document')
    def test_upload_document_with_owner(self, mock_upload):
        """Test document upload with custom owner."""
        from app.model import UploadFileResponse, UploadFileRequest, DocumentType

        mock_request = UploadFileRequest(
            owner="test_user",
            model="openai:gpt-4",
            filename="filename.txt",
            document=DocumentContent(
                type=DocumentType.TEN_K,
                cik=320193,
                ticker="AAPL",
                filing_date="2023-11-03",
                report_date="2023-09-30",
                title="Apple Inc. 10-K",
                content="Sample 10-K content for testing"
            )
        )

        mock_upload.return_value = UploadFileResponse(
            owner="test_user",
            request=mock_request,
            status="OK"
        )

        request_data = {
            "owner": "test_user",
            "model": "openai:gpt-4",
            "filename": "filename.txt",
            "document": {
                "type": "10-k",
                "cik": 320193,
                "ticker": "AAPL",
                "filing_date": "2023-11-03",
                "report_date": "2023-09-30",
                "title": "Apple Inc. 10-K",
                "content": "Sample 10-K content for testing"
            }
        }

        response = self.client.post("/document/upload", json=request_data)

        assert response.status_code == 200
        mock_upload.assert_called_once()


class TestAnalysisEndpoints:
    """Test class for analysis-related endpoints."""

    def setup_method(self):
        """Set up test client before each test."""
        self.client = TestClient(app)

    @patch('app.AI_Controller.run_prompt')
    def test_run_analysis_async_success(self, mock_run_prompt):
        """Test successful asynchronous analysis run."""
        from app.model import AnalysisRunResponse, AnalysisRunRequest, PromptData

        # Create a proper request object for the mock
        mock_request = AnalysisRunRequest(
            prompt=PromptData(name="Test Analysis", model="grok:grok-3-latest"),
            tickers=["AAPL", "MSFT"]
        )

        # Mock the run_prompt function
        mock_run_prompt.return_value = AnalysisRunResponse(
            owner="system",
            request=mock_request,
            analysis_id="test-analysis-123",
            status="OK",
            message="Analysis started"
        )

        request_data = {
            "prompt": {
                "name": "Test Analysis",
                "model": "grok:grok-3-latest"
            },
            "tickers": ["AAPL", "MSFT"]
        }

        response = self.client.post("/analysis/run_async", json=request_data)

        assert response.status_code == 200
        data = response.json()
        assert data["analysis_id"] == "test-analysis-123"
        assert data["status"] == "OK"
        mock_run_prompt.assert_called_once()

    @patch('app.AI_Controller.run_prompt')
    def test_run_analysis_async_key_error(self, mock_run_prompt):
        """Test analysis run with KeyError (404 response)."""
        mock_run_prompt.side_effect = KeyError("Ticker not found")

        request_data = {
            "prompt": {
                "name": "Test Analysis",
                "model": "grok:grok-3-latest"
            },
            "tickers": ["INVALID"]
        }

        response = self.client.post("/analysis/run_async", json=request_data)

        assert response.status_code == 404
        assert "Ticker not found" in response.json()["detail"]

    @patch('app.AI_Controller.run_prompt')
    def test_run_analysis_async_general_error(self, mock_run_prompt):
        """Test analysis run with general error (500 response)."""
        mock_run_prompt.side_effect = Exception("Internal server error")

        request_data = {
            "prompt": {
                "name": "Test Analysis",
                "model": "grok:grok-3-latest"
            },
            "tickers": ["AAPL"]
        }

        response = self.client.post("/analysis/run_async", json=request_data)

        assert response.status_code == 500
        assert "Internal server error" in response.json()["detail"]

    @patch('app.AI_Controller.run_prompt_sync')
    def test_run_analysis_sync_success(self, mock_run_prompt_sync):
        """Test successful synchronous analysis run."""
        from app.model import AnalysisResult, PromptData

        # Mock the run_prompt_sync function
        mock_analysis_result = AnalysisResult(
            analysis_id="test-analysis-123",
            status="completed",
            prompt=PromptData(name="Test Sync Analysis", model="grok:grok-3-latest"),
            tickers=["AAPL"],
            started_at=datetime.now(),
            completed_at=datetime.now()
        )

        mock_run_prompt_sync.return_value = mock_analysis_result

        request_data = {
            "prompt": {
                "name": "Test Sync Analysis",
                "model": "grok:grok-3-latest"
            },
            "tickers": ["AAPL"]
        }

        response = self.client.post("/analysis/run_sync", json=request_data)

        assert response.status_code == 200
        data = response.json()
        assert data["result"]["analysis_id"] == "test-analysis-123"
        assert data["result"]["status"] == "completed"
        mock_run_prompt_sync.assert_called_once()

    @patch('app.AI_Controller.run_prompt_sync')
    def test_run_analysis_sync_key_error(self, mock_run_prompt_sync):
        """Test synchronous analysis run with KeyError (404 response)."""
        mock_run_prompt_sync.side_effect = KeyError("Ticker not found")

        request_data = {
            "prompt": {
                "name": "Test Analysis",
                "model": "grok:grok-3-latest"
            },
            "tickers": ["INVALID"]
        }

        response = self.client.post("/analysis/run_sync", json=request_data)

        assert response.status_code == 404
        assert "Ticker not found" in response.json()["detail"]

    @patch('app.AI_Controller.run_prompt_sync')
    def test_run_analysis_sync_general_error(self, mock_run_prompt_sync):
        """Test synchronous analysis run with general error (500 response)."""
        mock_run_prompt_sync.side_effect = Exception("Processing error")

        request_data = {
            "prompt": {
                "name": "Test Analysis",
                "model": "grok:grok-3-latest"
            },
            "tickers": ["AAPL"]
        }

        response = self.client.post("/analysis/run_sync", json=request_data)

        assert response.status_code == 500
        assert "Processing error" in response.json()["detail"]

    @patch('app.AI_Controller.get_analysis_results')
    def test_get_analysis_results_success(self, mock_get_results):
        """Test successful retrieval of analysis results."""
        from app.model import AnalysisResult, PromptData

        # Mock the get_analysis_results function
        mock_analysis_result = AnalysisResult(
            analysis_id="test-analysis-123",
            status="completed",
            prompt=PromptData(name="Test Analysis", model="grok:grok-3-latest"),
            tickers=["AAPL"],
            started_at=datetime.now(),
            completed_at=datetime.now()
        )

        mock_get_results.return_value = mock_analysis_result

        response = self.client.get("/analysis/result/test-analysis-123")

        assert response.status_code == 200
        data = response.json()
        assert data["result"]["analysis_id"] == "test-analysis-123"
        assert data["result"]["status"] == "completed"
        mock_get_results.assert_called_once_with("test-analysis-123")

    @patch('app.AI_Controller.get_analysis_results')
    def test_get_analysis_results_not_found(self, mock_get_results):
        """Test analysis results not found (404 response)."""
        mock_get_results.side_effect = Exception("Analysis not found")

        response = self.client.get("/analysis/result/nonexistent-id")

        assert response.status_code == 404
        assert "Analysis not found" in response.json()["detail"]

    @patch('app.AI_Controller.get_analysis_run_list')
    def test_get_analysis_run_list_success(self, mock_get_list):
        """Test successful retrieval of analysis run list."""
        from app.model import AnalysisRunListResponse

        # Mock the get_analysis_run_list function
        mock_get_list.return_value = AnalysisRunListResponse(
            status="OK",
            message="Analysis list retrieved",
            ids=["analysis-1", "analysis-2", "analysis-3"]
        )

        response = self.client.get("/analysis/list")

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "OK"
        assert len(data["ids"]) == 3
        assert "analysis-1" in data["ids"]
        mock_get_list.assert_called_once()

    @patch('app.AI_Controller.get_analysis_run_list')
    def test_get_analysis_run_list_key_error(self, mock_get_list):
        """Test analysis run list with KeyError (404 response)."""
        mock_get_list.side_effect = KeyError("No analyses found")

        response = self.client.get("/analysis/list")

        assert response.status_code == 404
        assert "No analyses found" in response.json()["detail"]

    @patch('app.AI_Controller.get_analysis_run_list')
    def test_get_analysis_run_list_general_error(self, mock_get_list):
        """Test analysis run list with general error (500 response)."""
        mock_get_list.side_effect = Exception("Database error")

        response = self.client.get("/analysis/list")

        assert response.status_code == 500
        assert "Database error" in response.json()["detail"]

    def test_cancel_queued_analysis(self):
        """Test cancelling queued analysis."""
        request_data = {
            "owner": "test_user"
        }

        response = self.client.post("/analysis/cancel-queued/", json=request_data)

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "OK"
        assert "Cancelled queued analyses" in data["message"]

    def test_cancel_queued_analysis_empty_request(self):
        """Test cancelling queued analysis with empty request."""
        request_data = {}

        response = self.client.post("/analysis/cancel-queued/", json=request_data)

        assert response.status_code == 200
        data = response.json()
        assert "Cancelled queued analyses" in data["message"]


def main():
    """Main function to run the app tests."""
    print("Running comprehensive FastAPI app tests...")
    import pytest
    pytest.main([__file__, "-v"])


if __name__ == "__main__":
    main()

"""
Comprehensive unit tests for the AI_Controller module.
"""
import os
import sys
from unittest.mock import Mock, patch

import pytest
from openai import RateLimitError

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import AI_Controller
from app.model import (
    AnalysisRunRequest, AnalysisRunResponse, AnalysisResult, UploadFileResponse, DocumentRef, PromptData, PromptStepData, DocumentType, AnalysisRunListResponse, TextRequest
)


class TestAIControllerPublicFunctions:
    """Test class for public functions in AI_Controller."""

    def setup_method(self):
        """Set up test environment before each test."""
        # Reset global state
        AI_Controller._reset_global_cancel()

    def test_upload_document_success(self, sample_upload_request):
        """Test successful document upload."""
        owner = "test_user"

        result = AI_Controller.upload_document(owner, sample_upload_request)

        assert isinstance(result, UploadFileResponse)
        assert result.owner == owner
        assert result.request == sample_upload_request

    @patch('app.AI_Controller.analysis_store')
    @patch('app.AI_Controller.document_store')
    @patch('app.AI_Controller.sec_controller')
    def test_run_prompt_success(self, mock_sec, mock_doc_store, mock_analysis_store, sample_analysis_request):
        """Test successful asynchronous analysis run."""
        owner = "test_user"
        analysis_id = "test-analysis-123"

        # Mock dependencies
        mock_sec.symbol_store.get_cik.return_value = 320193
        mock_filing = Mock()
        mock_filing.form = "10-K"
        mock_filing.accession_number = "0000320193-23-000064"
        mock_sec.get_company_filings.return_value = [mock_filing]

        mock_analysis_store.create.return_value = analysis_id

        result = AI_Controller.run_prompt(owner, sample_analysis_request)

        assert isinstance(result, AnalysisRunResponse)
        assert result.owner == owner
        assert result.request == sample_analysis_request
        assert result.analysis_id == analysis_id

        # Verify store interactions
        mock_analysis_store.create.assert_called_once()

    @patch('app.AI_Controller.analysis_store')
    @patch('app.AI_Controller.document_store')
    @patch('app.AI_Controller.sec_controller')
    @patch('app.AI_Controller._run_in_background')
    def test_run_prompt_sync_success(self, mock_run_bg, mock_sec, mock_doc_store, mock_analysis_store, sample_analysis_request):
        """Test successful synchronous analysis run."""
        owner = "test_user"
        analysis_id = "test-analysis-123"

        # Mock dependencies
        mock_sec.symbol_store.get_cik.return_value = 320193
        mock_filing = Mock()
        mock_filing.form = "10-K"
        mock_filing.accession_number = "0000320193-23-000064"
        mock_sec.get_company_filings.return_value = [mock_filing]

        mock_analysis_store.create.return_value = analysis_id

        # Mock the background run result
        mock_result = AnalysisResult(
            analysis_id=analysis_id,
            status="completed",
            prompt=sample_analysis_request.prompt,
            tickers=sample_analysis_request.tickers
        )
        mock_run_bg.return_value = mock_result

        result = AI_Controller.run_prompt_sync(owner, sample_analysis_request)

        assert isinstance(result, AnalysisResult)
        assert result.analysis_id == analysis_id
        assert result.status == "completed"

        # Verify background run was called
        mock_run_bg.assert_called_once()

    @patch('app.AI_Controller.analysis_store')
    def test_get_analysis_results_success(self, mock_analysis_store):
        """Test successful retrieval of analysis results."""
        analysis_id = "test-analysis-123"

        # Mock the analysis result
        mock_result = AnalysisResult(
            analysis_id=analysis_id,
            status="completed",
            prompt=PromptData(name="Test Analysis", model="grok:grok-3-latest"),
            tickers=["AAPL"]
        )
        mock_analysis_store.get_result.return_value = mock_result

        result = AI_Controller.get_analysis_results(analysis_id)

        assert isinstance(result, AnalysisResult)
        assert result.analysis_id == analysis_id
        assert result.status == "completed"

        mock_analysis_store.get_result.assert_called_once_with(analysis_id)

    @patch('app.AI_Controller.analysis_store')
    def test_get_analysis_results_not_found(self, mock_analysis_store):
        """Test analysis results not found."""
        analysis_id = "nonexistent-id"

        mock_analysis_store.get_result.return_value = None

        with pytest.raises(Exception, match="Analysis not found"):
            AI_Controller.get_analysis_results(analysis_id)

    @patch('app.AI_Controller.analysis_store')
    def test_get_analysis_run_list_success(self, mock_analysis_store):
        """Test successful retrieval of analysis run list."""
        mock_ids = ["analysis-1", "analysis-2", "analysis-3"]
        mock_analysis_store.get_analysis_ids.return_value = mock_ids

        result = AI_Controller.get_analysis_run_list()

        assert isinstance(result, AnalysisRunListResponse)
        assert result.ids == mock_ids

        mock_analysis_store.get_analysis_ids.assert_called_once()

    def test_cancel_queued_success(self):
        """Test successful cancellation of queued analyses."""
        result = AI_Controller.cancel_queued()

        assert result['status'] == 'cancelling'
        assert 'Cancelling queued analyses' in result['message']
        assert AI_Controller.global_cancel_queued is True

    @patch('app.Grok_Controller.summarize')
    def test_summarize_success(self, mock_grok_summarize):
        """Test successful text summarization."""
        mock_grok_summarize.return_value = "This is a summary"

        request = TextRequest(text="Long text to summarize", language="en", max_tokens=100)
        result = AI_Controller.summarize(request)

        assert result == "This is a summary"
        mock_grok_summarize.assert_called_once_with(
            text=request.text,
            language=request.language,
            max_tokens=request.max_tokens
        )

    @patch('app.Grok_Controller.translate')
    def test_translate_success(self, mock_grok_translate):
        """Test successful text translation."""
        mock_grok_translate.return_value = "Translated text"

        request = TextRequest(text="Text to translate", language="es")
        result = AI_Controller.translate(request)

        assert result == "Translated text"
        mock_grok_translate.assert_called_once_with(
            text=request.text,
            language=request.language,
            max_tokens=request.max_tokens
        )


class TestAIControllerPrivateFunctions:
    """Test class for private functions in AI_Controller."""

    def setup_method(self):
        """Set up test environment before each test."""
        AI_Controller._reset_global_cancel()

    def test_check_global_cancel_not_cancelled(self):
        """Test _check_global_cancel when not cancelled."""
        AI_Controller._reset_global_cancel()

        # Should not raise an exception
        AI_Controller._check_global_cancel()

    def test_check_global_cancel_cancelled(self):
        """Test _check_global_cancel when cancelled."""
        AI_Controller.cancel_queued()

        with pytest.raises(Exception, match="Canceled by user"):
            AI_Controller._check_global_cancel()

    def test_reset_global_cancel(self):
        """Test _reset_global_cancel function."""
        AI_Controller.cancel_queued()
        assert AI_Controller.global_cancel_queued is True

        AI_Controller._reset_global_cancel()
        assert AI_Controller.global_cancel_queued is False

    def test_normalize_ticker_valid_list(self, sample_analysis_request):
        """Test _normalize_ticker with valid ticker list."""
        result = AI_Controller._normalize_ticker(sample_analysis_request)

        assert result == ["AAPL", "MSFT"]

    def test_normalize_ticker_no_tickers(self):
        """Test _normalize_ticker with no tickers."""
        request = AnalysisRunRequest(
            prompt=PromptData(name="Test", model="grok:grok-3-latest"),
            tickers=None
        )

        with pytest.raises(Exception, match="No tickers found"):
            AI_Controller._normalize_ticker(request)

    @patch('app.AI_Controller.sec_controller')
    def test_build_documents_list_success(self, mock_sec, sample_prompt_data):
        """Test _build_documents_list with valid inputs."""
        # Mock SEC controller
        mock_sec.symbol_store.get_cik.return_value = 320193
        mock_filing = Mock()
        mock_filing.form = "10-K"
        mock_filing.accession_number = "0000320193-23-000064"
        mock_sec.get_company_filings.return_value = [mock_filing]

        result = AI_Controller._build_documents_list(sample_prompt_data, ["AAPL"])

        assert len(result) == 1
        assert isinstance(result[0], DocumentRef)
        assert result[0].type == DocumentType.TEN_K
        assert result[0].cik == 320193
        assert result[0].ticker == "AAPL"
        assert result[0].accession_number == "0000320193-23-000064"

    @patch('app.AI_Controller.analysis_store')
    def test_report_exception_with_analysis_id(self, mock_analysis_store):
        """Test _report_exception with existing analysis ID."""
        owner = "test_user"
        analysis_id = "test-analysis-123"
        request = AnalysisRunRequest(
            prompt=PromptData(name="Test", model="grok:grok-3-latest"),
            tickers=["AAPL"]
        )
        exception = Exception("Test error")

        # Mock analysis result
        mock_result = Mock()
        mock_analysis_store.get_result.return_value = mock_result

        AI_Controller._report_exception(owner, analysis_id, request, exception)

        assert mock_result.error == "Test error"
        assert mock_result.completed_at is not None
        mock_analysis_store.get_result.assert_called_once_with(analysis_id)

    @patch('app.AI_Controller.analysis_store')
    def test_report_exception_without_analysis_id(self, mock_analysis_store):
        """Test _report_exception without analysis ID."""
        owner = "test_user"
        analysis_id = None
        request = AnalysisRunRequest(
            prompt=PromptData(name="Test", model="grok:grok-3-latest"),
            tickers=["AAPL"]
        )
        exception = Exception("Test error")

        # Mock store responses
        mock_analysis_store.create.return_value = "new-analysis-123"
        mock_result = Mock()
        mock_analysis_store.get_result.return_value = mock_result

        AI_Controller._report_exception(owner, analysis_id, request, exception)

        mock_analysis_store.create.assert_called_once_with(owner, request, doc_refs=[])
        assert mock_result.error == "Test error"
        assert mock_result.completed_at is not None


class TestAIControllerStepExecution:
    """Test class for step execution functionality in AI_Controller."""

    def setup_method(self):
        """Set up test environment before each test."""
        AI_Controller._reset_global_cancel()

    @patch('app.AI_Controller.analysis_store')
    @patch('app.Grok_Controller.run_langchain')
    def test_run_steps_grok_completion_success(self, mock_grok, mock_analysis_store):
        """Test _run_steps with Grok completion model."""
        # Setup
        request = AnalysisRunRequest(
            prompt=PromptData(
                name="Test Analysis",
                model="grok:grok-3-latest",
                steps=[
                    PromptStepData(
                        system_prompt="You are a financial analyst",
                        user_prompt="Analyze the data",
                        model_action="completion",
                        response_format="text"
                    )
                ]
            ),
            tickers=["AAPL"]
        )
        analysis_id = "test-analysis-123"
        documents = []

        # Mock Grok response
        mock_grok.return_value = ("Analysis complete", Mock())

        # Mock analysis store
        mock_result = Mock()
        mock_analysis_store.get_result.return_value = mock_result

        result = AI_Controller._run_steps(request, analysis_id, documents)

        assert isinstance(result, AnalysisResult)
        assert result.analysis_id == analysis_id
        assert result.status == 'running'
        assert len(result.results) == 1
        assert result.results[0].output == "Analysis complete"

        # Verify Grok was called
        mock_grok.assert_called_once()

        # Verify analysis store updates
        assert mock_analysis_store.update.call_count >= 2  # At least initial and final updates

    @patch('app.AI_Controller.analysis_store')
    @patch('app.OpenAI_Controller.run_completion_api')
    def test_run_steps_openai_completion_success(self, mock_openai, mock_analysis_store):
        """Test _run_steps with OpenAI completion model."""
        # Setup
        request = AnalysisRunRequest(
            prompt=PromptData(
                name="Test Analysis",
                model="openai:gpt-4",
                steps=[
                    PromptStepData(
                        system_prompt="You are a financial analyst",
                        user_prompt="Analyze the data",
                        model_action="completion",
                        response_format="text"
                    )
                ]
            ),
            tickers=["AAPL"]
        )
        analysis_id = "test-analysis-123"
        documents = []

        # Mock OpenAI response
        mock_openai.return_value = ("OpenAI analysis complete", Mock())

        # Mock analysis store
        mock_result = Mock()
        mock_analysis_store.get_result.return_value = mock_result

        result = AI_Controller._run_steps(request, analysis_id, documents)

        assert isinstance(result, AnalysisResult)
        assert result.analysis_id == analysis_id
        assert len(result.results) == 1
        assert result.results[0].output == "OpenAI analysis complete"

        # Verify OpenAI was called
        mock_openai.assert_called_once()

    @patch('app.AI_Controller.analysis_store')
    @patch('app.OpenAI_Controller.run_response_api')
    def test_run_steps_openai_response_action(self, mock_openai, mock_analysis_store):
        """Test _run_steps with OpenAI response action."""
        # Setup
        request = AnalysisRunRequest(
            prompt=PromptData(
                name="Test Analysis",
                model="openai:gpt-4",
                steps=[
                    PromptStepData(
                        system_prompt="You are a financial analyst",
                        user_prompt="Analyze the data",
                        model_action="response",
                        response_format="text"
                    )
                ]
            ),
            tickers=["AAPL"]
        )
        analysis_id = "test-analysis-123"
        documents = []

        # Mock OpenAI response
        mock_openai.return_value = ("Response analysis complete", Mock())

        # Mock analysis store
        mock_result = Mock()
        mock_analysis_store.get_result.return_value = mock_result

        result = AI_Controller._run_steps(request, analysis_id, documents)

        assert isinstance(result, AnalysisResult)
        assert len(result.results) == 1
        assert result.results[0].output == "Response analysis complete"

        # Verify OpenAI response API was called
        mock_openai.assert_called_once()

    @patch('app.AI_Controller.analysis_store')
    def test_run_steps_empty_reply_error(self, mock_analysis_store):
        """Test _run_steps with empty reply from model."""
        # Setup
        request = AnalysisRunRequest(
            prompt=PromptData(
                name="Test Analysis",
                model="unknown:model",
                steps=[
                    PromptStepData(
                        system_prompt="You are a financial analyst",
                        user_prompt="Analyze the data",
                        model_action="completion",
                        response_format="text"
                    )
                ]
            ),
            tickers=["AAPL"]
        )
        analysis_id = "test-analysis-123"
        documents = []

        # Mock analysis store
        mock_result = Mock()
        mock_analysis_store.get_result.return_value = mock_result

        with pytest.raises(Exception, match="Empty reply for step 1"):
            AI_Controller._run_steps(request, analysis_id, documents)

    @patch('app.AI_Controller.analysis_store')
    @patch('app.Grok_Controller.run_langchain')
    def test_run_steps_multiple_steps(self, mock_grok, mock_analysis_store):
        """Test _run_steps with multiple steps."""
        # Setup
        request = AnalysisRunRequest(
            prompt=PromptData(
                name="Test Analysis",
                model="grok:grok-3-latest",
                steps=[
                    PromptStepData(
                        system_prompt="You are a financial analyst",
                        user_prompt="Analyze the revenue",
                        model_action="completion",
                        response_format="text"
                    ),
                    PromptStepData(
                        system_prompt="You are a financial analyst",
                        user_prompt="Analyze the expenses",
                        model_action="completion",
                        response_format="text"
                    )
                ]
            ),
            tickers=["AAPL"]
        )
        analysis_id = "test-analysis-123"
        documents = []

        # Mock Grok responses
        mock_grok.side_effect = [
            ("Revenue analysis complete", Mock()),
            ("Expense analysis complete", Mock())
        ]

        # Mock analysis store
        mock_result = Mock()
        mock_analysis_store.get_result.return_value = mock_result

        result = AI_Controller._run_steps(request, analysis_id, documents)

        assert isinstance(result, AnalysisResult)
        assert len(result.results) == 2
        assert result.results[0].output == "Revenue analysis complete"
        assert result.results[1].output == "Expense analysis complete"
        assert result.results[0].step_number == 1
        assert result.results[1].step_number == 2

        # Verify Grok was called twice
        assert mock_grok.call_count == 2

    @patch('app.AI_Controller.analysis_store')
    @patch('app.Grok_Controller.run_langchain')
    def test_run_steps_cancelled_during_execution(self, mock_grok, mock_analysis_store):
        """Test _run_steps when cancelled during execution."""
        # Setup
        request = AnalysisRunRequest(
            prompt=PromptData(
                name="Test Analysis",
                model="grok:grok-3-latest",
                steps=[
                    PromptStepData(
                        system_prompt="You are a financial analyst",
                        user_prompt="Analyze the data",
                        model_action="completion",
                        response_format="text"
                    )
                ]
            ),
            tickers=["AAPL"]
        )
        analysis_id = "test-analysis-123"
        documents = []

        # Mock analysis store
        mock_result = Mock()
        mock_analysis_store.get_result.return_value = mock_result

        # Cancel the operation
        AI_Controller.cancel_queued()

        with pytest.raises(Exception, match="Canceled by user"):
            AI_Controller._run_steps(request, analysis_id, documents)


class TestAIControllerBackgroundExecution:
    """Test class for background execution functionality in AI_Controller."""

    def setup_method(self):
        """Set up test environment before each test."""
        AI_Controller._reset_global_cancel()

    @patch('app.AI_Controller.analysis_store')
    @patch('app.AI_Controller.document_store')
    @patch('app.AI_Controller._run_steps')
    def test_run_in_background_success(self, mock_run_steps, mock_doc_store, mock_analysis_store):
        """Test _run_in_background with successful execution."""
        owner = "test_user"
        request = AnalysisRunRequest(
            prompt=PromptData(name="Test", model="grok:grok-3-latest"),
            tickers=["AAPL"]
        )
        doc_list = []
        analysis_id = "test-analysis-123"

        # Mock document store
        mock_doc_store.get_documents.return_value = []

        # Mock successful run_steps
        mock_result = AnalysisResult(
            analysis_id=analysis_id,
            status="completed",
            prompt=request.prompt,
            tickers=request.tickers
        )
        mock_run_steps.return_value = mock_result

        result = AI_Controller._run_in_background(owner, request, doc_list, analysis_id)

        assert result == mock_result
        mock_run_steps.assert_called_once()
        mock_analysis_store.update.assert_called_with(analysis_id, status='completed', results=mock_result)

    @patch('app.AI_Controller.analysis_store')
    @patch('app.AI_Controller.document_store')
    @patch('app.AI_Controller._run_steps')
    @patch('time.sleep')  # Mock sleep to speed up tests
    def test_run_in_background_rate_limit_retry(self, mock_sleep, mock_run_steps, mock_doc_store, mock_analysis_store):
        """Test _run_in_background with rate limit error and retry."""
        owner = "test_user"
        request = AnalysisRunRequest(
            prompt=PromptData(name="Test", model="grok:grok-3-latest"),
            tickers=["AAPL"]
        )
        doc_list = []
        analysis_id = "test-analysis-123"

        # Mock document store
        mock_doc_store.get_documents.return_value = []

        # Mock rate limit error then success
        rate_limit_error = RateLimitError(
            message="Rate limit exceeded",
            response=Mock(),
            body={}
        )
        rate_limit_error.code = "rate_limit_exceeded"

        mock_result = AnalysisResult(
            analysis_id=analysis_id,
            status="completed",
            prompt=request.prompt,
            tickers=request.tickers
        )

        mock_run_steps.side_effect = [rate_limit_error, mock_result]

        result = AI_Controller._run_in_background(owner, request, doc_list, analysis_id)

        assert result == mock_result
        assert mock_run_steps.call_count == 2
        mock_sleep.assert_called_once_with(20)  # Verify sleep was called

    @patch('app.AI_Controller.analysis_store')
    @patch('app.AI_Controller.document_store')
    @patch('app.AI_Controller._run_steps')
    @patch('time.sleep')
    def test_run_in_background_insufficient_quota(self, mock_sleep, mock_run_steps, mock_doc_store, mock_analysis_store):
        """Test _run_in_background with insufficient quota error."""
        owner = "test_user"
        request = AnalysisRunRequest(
            prompt=PromptData(name="Test", model="grok:grok-3-latest"),
            tickers=["AAPL"]
        )
        doc_list = []
        analysis_id = "test-analysis-123"

        # Mock document store
        mock_doc_store.get_documents.return_value = []

        # Mock insufficient quota error
        rate_limit_error = RateLimitError(
            message="Insufficient quota",
            response=Mock(),
            body={}
        )
        rate_limit_error.code = "insufficient_quota"

        mock_run_steps.side_effect = rate_limit_error

        result = AI_Controller._run_in_background(owner, request, doc_list, analysis_id)

        assert result is None
        mock_analysis_store.update.assert_called_with(analysis_id, status='failed', error='RateLimitError(_run_steps): Insufficient quota')


    @patch('app.AI_Controller.analysis_store')
    @patch('app.AI_Controller.document_store')
    @patch('app.AI_Controller._run_steps')
    def test_run_in_background_general_exception(self, mock_run_steps, mock_doc_store, mock_analysis_store):
        """Test _run_in_background with general exception."""
        owner = "test_user"
        request = AnalysisRunRequest(
            prompt=PromptData(name="Test", model="grok:grok-3-latest"),
            tickers=["AAPL"]
        )
        doc_list = []
        analysis_id = "test-analysis-123"

        # Mock document store
        mock_doc_store.get_documents.return_value = []

        # Mock general exception
        mock_run_steps.side_effect = Exception("General error")

        result = AI_Controller._run_in_background(owner, request, doc_list, analysis_id)

        assert result is None
        mock_analysis_store.update.assert_called_with(analysis_id, status='failed', error='General error')

    @patch('app.AI_Controller.analysis_store')
    @patch('app.AI_Controller.document_store')
    @patch('app.AI_Controller._run_steps')
    def test_run_in_background_cancelled(self, mock_run_steps, mock_doc_store, mock_analysis_store):
        """Test _run_in_background when cancelled."""
        owner = "test_user"
        request = AnalysisRunRequest(
            prompt=PromptData(name="Test", model="grok:grok-3-latest"),
            tickers=["AAPL"]
        )
        doc_list = []
        analysis_id = "test-analysis-123"

        # Mock document store
        mock_doc_store.get_documents.return_value = []

        # Cancel the operation
        AI_Controller.cancel_queued()

        result = AI_Controller._run_in_background(owner, request, doc_list, analysis_id)

        assert result is None
        mock_analysis_store.update.assert_called_with(analysis_id, status='failed', error='Canceled by user')


class TestAIControllerErrorHandling:
    """Test class for error handling in AI_Controller."""

    def setup_method(self):
        """Set up test environment before each test."""
        AI_Controller._reset_global_cancel()


    @patch('app.AI_Controller.sec_controller')
    def test_build_documents_list_no_matching_documents(self, mock_sec):
        """Test _build_documents_list with no matching documents."""
        # Mock SEC controller with no matching filings
        mock_sec.symbol_store.get_cik.return_value = 320193
        mock_filing = Mock()
        mock_filing.form = "8-K"  # Different form type
        mock_filing.accession_number = "0000320193-23-000064"
        mock_sec.get_company_filings.return_value = [mock_filing]

        prompt = PromptData(
            name="Test",
            model="grok:grok-3-latest",
            document_types=[DocumentType.TEN_K]  # Looking for 10-K but only 8-K available
        )

        result = AI_Controller._build_documents_list(prompt, ["AAPL"])

        assert len(result) == 0  # No matching documents

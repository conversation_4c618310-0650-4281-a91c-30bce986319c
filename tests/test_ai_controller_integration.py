"""
Integration tests for the AI_Controller module.
These tests verify the integration between AI_Controller and its dependencies.
"""
import os
import sys
from unittest.mock import Mock, patch

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import AI_Controller
from app.model import (
    AnalysisRunRequest, AnalysisRunResponse, AnalysisResult,
    PromptData, PromptStepData, DocumentType, DocumentContent
)


class TestAIControllerIntegration:
    """Integration tests for AI_Controller with real-like scenarios."""

    def setup_method(self):
        """Set up test environment before each test."""
        AI_Controller._reset_global_cancel()

    @patch('app.AI_Controller.analysis_store')
    @patch('app.AI_Controller.document_store')
    @patch('app.AI_Controller.sec_controller')
    @patch('app.Grok_Controller.run_langchain')
    def test_full_analysis_workflow_grok(self, mock_grok, mock_sec, mock_doc_store, mock_analysis_store):
        """Test complete analysis workflow with Grok model."""
        owner = "test_user"
        analysis_id = "test-analysis-123"
        
        # Setup request
        request = AnalysisRunRequest(
            prompt=PromptData(
                name="Financial Analysis",
                model="grok:grok-3-latest",
                steps=[
                    PromptStepData(
                        system_prompt="You are a financial analyst",
                        user_prompt="Analyze the company's revenue trends",
                        model_action="completion",
                        response_format="text"
                    ),
                    PromptStepData(
                        system_prompt="You are a financial analyst",
                        user_prompt="Provide investment recommendations",
                        model_action="completion",
                        response_format="text"
                    )
                ],
                document_types=[DocumentType.TEN_K]
            ),
            tickers=["AAPL"]
        )
        
        # Mock SEC controller
        mock_sec.symbol_store.get_cik.return_value = 320193
        mock_filing = Mock()
        mock_filing.form = "10-K"
        mock_filing.accession_number = "0000320193-23-000064"
        mock_sec.get_company_filings.return_value = [mock_filing]
        
        # Mock document store
        mock_document = DocumentContent(
            type=DocumentType.TEN_K,
            cik=320193,
            ticker="AAPL",
            filing_date="2023-11-03",
            report_date="2023-09-30",
            title="Apple Inc. 10-K",
            content="Apple's revenue increased by 15% year-over-year..."
        )
        mock_doc_store.get_documents.return_value = [mock_document]
        
        # Mock analysis store
        mock_analysis_store.create.return_value = analysis_id
        mock_result = Mock()
        mock_analysis_store.get_result.return_value = mock_result
        
        # Mock Grok responses
        mock_grok.side_effect = [
            ("Revenue analysis: Apple shows strong growth...", Mock()),
            ("Investment recommendation: Buy with target price $200", Mock())
        ]
        
        # Execute synchronous analysis
        result = AI_Controller.run_prompt_sync(owner, request)
        
        # Verify results
        assert isinstance(result, AnalysisResult)
        assert result.analysis_id == analysis_id
        assert len(result.results) == 2
        assert "Revenue analysis" in result.results[0].output
        assert "Investment recommendation" in result.results[1].output
        
        # Verify interactions
        mock_sec.symbol_store.get_cik.assert_called_with("AAPL")
        mock_sec.get_company_filings.assert_called_with(320193)
        mock_doc_store.get_documents.assert_called_once()
        assert mock_grok.call_count == 2

    @patch('app.AI_Controller.analysis_store')
    @patch('app.AI_Controller.document_store')
    @patch('app.AI_Controller.sec_controller')
    @patch('app.OpenAI_Controller.run_completion_api')
    def test_full_analysis_workflow_openai(self, mock_openai, mock_sec, mock_doc_store, mock_analysis_store):
        """Test complete analysis workflow with OpenAI model."""
        owner = "test_user"
        analysis_id = "test-analysis-456"
        
        # Setup request
        request = AnalysisRunRequest(
            prompt=PromptData(
                name="Risk Analysis",
                model="openai:gpt-4",
                steps=[
                    PromptStepData(
                        system_prompt="You are a risk analyst",
                        user_prompt="Identify key financial risks",
                        model_action="completion",
                        response_format="text"
                    )
                ],
                document_types=[DocumentType.TEN_K, DocumentType.TEN_Q]
            ),
            tickers=["MSFT", "GOOGL"]
        )
        
        # Mock SEC controller for multiple tickers
        mock_sec.symbol_store.get_cik.side_effect = [789019, 1652044]  # MSFT, GOOGL CIKs
        
        # Mock filings for both companies
        msft_filing = Mock()
        msft_filing.form = "10-K"
        msft_filing.accession_number = "0000789019-23-000001"
        
        googl_filing = Mock()
        googl_filing.form = "10-K"
        googl_filing.accession_number = "0001652044-23-000001"
        
        mock_sec.get_company_filings.side_effect = [[msft_filing], [googl_filing]]
        
        # Mock document store
        mock_documents = [
            DocumentContent(
                type=DocumentType.TEN_K,
                cik=789019,
                ticker="MSFT",
                content="Microsoft's cloud business shows strong growth..."
            ),
            DocumentContent(
                type=DocumentType.TEN_K,
                cik=1652044,
                ticker="GOOGL",
                content="Google's advertising revenue faces headwinds..."
            )
        ]
        mock_doc_store.get_documents.return_value = mock_documents
        
        # Mock analysis store
        mock_analysis_store.create.return_value = analysis_id
        mock_result = Mock()
        mock_analysis_store.get_result.return_value = mock_result
        
        # Mock OpenAI response
        mock_openai.return_value = ("Risk analysis: Key risks include regulatory changes...", Mock())
        
        # Execute synchronous analysis
        result = AI_Controller.run_prompt_sync(owner, request)
        
        # Verify results
        assert isinstance(result, AnalysisResult)
        assert result.analysis_id == analysis_id
        assert len(result.results) == 1
        assert "Risk analysis" in result.results[0].output
        
        # Verify multiple ticker handling
        assert mock_sec.symbol_store.get_cik.call_count == 2
        assert mock_sec.get_company_filings.call_count == 2

    @patch('app.AI_Controller.analysis_store')
    @patch('app.AI_Controller.document_store')
    @patch('app.AI_Controller.sec_controller')
    @patch('threading.Thread')
    def test_asynchronous_analysis_execution(self, mock_thread, mock_sec, mock_doc_store, mock_analysis_store):
        """Test asynchronous analysis execution with threading."""
        owner = "test_user"
        analysis_id = "test-analysis-789"
        
        # Setup request
        request = AnalysisRunRequest(
            prompt=PromptData(
                name="Async Analysis",
                model="grok:grok-3-latest",
                steps=[
                    PromptStepData(
                        system_prompt="You are an analyst",
                        user_prompt="Analyze the data",
                        model_action="completion",
                        response_format="text"
                    )
                ],
                document_types=[DocumentType.TEN_K]
            ),
            tickers=["AAPL"]
        )
        
        # Mock SEC controller
        mock_sec.symbol_store.get_cik.return_value = 320193
        mock_filing = Mock()
        mock_filing.form = "10-K"
        mock_filing.accession_number = "0000320193-23-000064"
        mock_sec.get_company_filings.return_value = [mock_filing]
        
        # Mock analysis store
        mock_analysis_store.create.return_value = analysis_id
        
        # Mock thread
        mock_thread_instance = Mock()
        mock_thread.return_value = mock_thread_instance
        
        # Execute asynchronous analysis
        result = AI_Controller.run_prompt(owner, request)
        
        # Verify response
        assert isinstance(result, AnalysisRunResponse)
        assert result.owner == owner
        assert result.request == request
        assert result.analysis_id == analysis_id
        
        # Verify thread was created and started
        # mock_thread.assert_called_once()
        # mock_thread_instance.start.assert_called_once()
        
        # Verify analysis was created in store
        mock_analysis_store.create.assert_called_once()

    @patch('app.AI_Controller.analysis_store')
    @patch('app.AI_Controller.document_store')
    @patch('app.AI_Controller.sec_controller')
    def test_document_building_integration(self, mock_sec, mock_doc_store, mock_analysis_store):
        """Test document building integration with SEC controller."""
        # Setup prompt with multiple document types
        prompt = PromptData(
            name="Multi-Document Analysis",
            model="grok:grok-3-latest",
            document_types=[DocumentType.TEN_K, DocumentType.TEN_Q, DocumentType.EIGHT_K]
        )
        
        # Mock SEC controller with various filing types
        mock_sec.symbol_store.get_cik.return_value = 320193
        
        mock_filings = [
            Mock(form="10-K", accession_number="0000320193-23-000064"),
            Mock(form="10-Q", accession_number="0000320193-23-000032"),
            Mock(form="8-K", accession_number="0000320193-23-000015"),
            Mock(form="DEF 14A", accession_number="0000320193-23-000001")  # Should be filtered out
        ]
        mock_sec.get_company_filings.return_value = mock_filings
        
        # Execute document building
        result = AI_Controller._build_documents_list(prompt, ["AAPL"])
        
        # Verify results
        assert len(result) == 3  # Only 10-K, 10-Q, and 8-K should be included
        
        # Verify document types
        doc_types = [doc.type for doc in result]
        assert DocumentType.TEN_K in doc_types
        assert DocumentType.TEN_Q in doc_types
        assert DocumentType.EIGHT_K in doc_types
        
        # Verify all documents have correct ticker and CIK
        for doc in result:
            assert doc.ticker == "AAPL"
            assert doc.cik == 320193

    @patch('app.AI_Controller.analysis_store')
    def test_analysis_lifecycle_management(self, mock_analysis_store):
        """Test complete analysis lifecycle management."""
        analysis_id = "test-lifecycle-123"
        
        # Mock analysis result that evolves through lifecycle
        mock_result = Mock()
        mock_result.status = "pending"
        mock_analysis_store.get_result.return_value = mock_result
        
        # Test getting analysis results
        result = AI_Controller.get_analysis_results(analysis_id)
        assert result == mock_result
        
        # Test getting analysis list
        mock_analysis_store.get_analysis_ids.return_value = [analysis_id, "other-analysis"]
        list_result = AI_Controller.get_analysis_run_list()
        assert analysis_id in list_result.ids
        
        # Test cancellation
        cancel_result = AI_Controller.cancel_queued()
        assert cancel_result['status'] == 'cancelling'
        assert AI_Controller.global_cancel_queued is True
        
        # Verify store interactions
        mock_analysis_store.get_result.assert_called_with(analysis_id)
        mock_analysis_store.get_analysis_ids.assert_called_once()

    @patch('app.Grok_Controller.summarize')
    @patch('app.Grok_Controller.translate')
    def test_text_processing_integration(self, mock_translate, mock_summarize):
        """Test text processing functions integration."""
        # Test summarization
        mock_summarize.return_value = "This is a summary of the financial report"
        
        from app.model import TextRequest
        summarize_request = TextRequest(
            text="Long financial report content...",
            language="en",
            max_tokens=500
        )
        
        summary_result = AI_Controller.summarize(summarize_request)
        assert summary_result == "This is a summary of the financial report"
        mock_summarize.assert_called_once_with(
            text=summarize_request.text,
            language=summarize_request.language,
            max_tokens=summarize_request.max_tokens
        )
        
        # Test translation
        mock_translate.return_value = "Esto es un resumen del informe financiero"
        
        translate_request = TextRequest(
            text="This is a summary of the financial report",
            language="es"
        )
        
        translate_result = AI_Controller.translate(translate_request)
        assert translate_result == "Esto es un resumen del informe financiero"
        mock_translate.assert_called_once_with(
            text=translate_request.text,
            language=translate_request.language,
            max_tokens=translate_request.max_tokens
        )

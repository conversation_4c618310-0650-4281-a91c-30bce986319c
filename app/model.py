from datetime import datetime
from enum import StrEnum
from typing import Any

from pydantic import BaseModel, Field


class DocumentType(StrEnum):
    NONE = "none"
    TEN_K = "10-k"
    TEN_Q = "10-q"
    EIGHT_K = "8-k"
    EXHIBIT = "exhibit"
    TRANSCRIPT = "transcript"
    NEWS = "news"
    USER_SUPPLIED = "user"

    @classmethod
    def _missing_(cls, value:str):
        value = value.lower()
        for member in cls:
            if member == value:
                return member
        return None

class DocumentRefType(StrEnum):
    NONE = "none"
    FILE_ID = "file_id"
    LINK = "link"
    LAST = "last"
    LAST_TWO = "last_2"
    ONE_YEAR = "1_year"
    TWO_YEARS = "2_year"
    ALL = "all"

    @classmethod
    def _missing_(cls, value:str):
        value = value.lower()
        for member in cls:
            if member == value:
                return member
        return None

class BaseRequest(BaseModel):
    owner: str = 'system'

class BaseResponse(BaseModel):
    status: str = "OK"
    message: str | None = None
    data: Any | None = None

class ErrorResponse(BaseResponse):
    request: BaseRequest | None = None
    status: str = "ERROR"
    errorCode: int = 500

# class SymbolRequest(BaseRequest):
#     symbol: str

# class SymbolResponse(BaseResponse):
#     request: SymbolRequest
#     cik: str | None = None
#     ticker: str | None = None
#     name: str | None = None

# class TickerSetData(BaseModel):
#     owner: str = 'system'
#     name: str | None = None
#     tickers: list[str] | None = None

class DocumentRef(BaseModel):
    type: DocumentType
    document_id : str | None = None
    cik: int | None = None
    ticker: str | None = None
    accession_number: str | None = None

class DocumentContent(BaseModel):
    type: DocumentType
    document_id : str | None = None
    cik: int | None = None
    ticker: str | None = None
    filing_date: str | None = None
    report_date: str | None = None
    title: str | None = None
    content: str | None = None

class PromptStepData(BaseModel):
    system_prompt: str | None = None
    user_prompt: str | None = None
    model_action: str = 'completion'
    input_format: str = 'text'
    response_format: str = 'text'
    tools: list[str] | None = []

class PromptData(BaseModel):
    _id: str | None = None
    name: str | None = None
    owner: str = 'system'
    model: str = 'grok:grok-3-latest'
    description: str | None = None
    steps: list[PromptStepData] | None = None
    document_types: list[DocumentType] | None = None
    vectorize: bool = False
    tag: str | None = None


# 10-k 2-years -> (FY2023, FY2024)
# file -> file_id
# link -> http://...html
# last -> last 10-k
# class DocumentSetData(BaseModel):
#     document_types: dict[DocumentType, list[str]] | None = None
#     # extra: list[list[str]] | None = Field(description="for IDs")  # for any ids
#     pass

class AnalysisRunRequest(BaseRequest):
    prompt: PromptData | None = None
    tickers: list[str] | None = None
    document_contents: list[DocumentContent] | None = None
    document_references: list[DocumentRef] | None = None
    tag: str | None = None

class AnalysisRunResponse(BaseResponse):
    owner: str = 'system'
    request: AnalysisRunRequest
    analysis_id: str
    document_list: list[DocumentRef] | None = None

class AnalysisStepResult(BaseModel):
    step_number: int
    step_data: PromptStepData
    output: str
    response_format: str
    started_at: datetime| None = None
    completed_at: datetime | None = None

class AnalysisResult(BaseModel):
    owner: str = 'system'
    analysis_id: str
    status: str
    error: str | None = None
    prompt: PromptData
    tickers: list[str] | None = None
    doc_refs: list[DocumentRef] | None = None
    results: list[AnalysisStepResult] | None = None
    started_at: datetime | None = None
    completed_at: datetime | None = None

class AnalysisResultResponse(BaseResponse):
    request: AnalysisRunRequest | None = None
    result: AnalysisResult

class UploadFileRequest(BaseRequest):
    model: str  # part before : is the provider, after is the model name. e.g. openai:gpt-4
    filename: str
    document: DocumentContent

class UploadFileResponse(BaseResponse):
    owner: str = 'system'
    request: UploadFileRequest
    document_id: str | None = None
    document_ref: DocumentRef | None = None

class CancelQueuedRequest(BaseRequest):
    pass

class CancelQueuedResponse(BaseResponse):
    request: CancelQueuedRequest
    pass

class ImprovePromptRequest(BaseRequest):
    user_prompt: str | None = None
    system_prompt: str | None = None

class ImprovePromptResponse(BaseResponse):
    request: ImprovePromptRequest
    improvement_user: str | None = None
    improvement_system: str | None = None

class SecFiling(BaseModel):
    accession_number: str
    cik: int
    ticker: str
    form: str
    primary_document: str
    filing_date: str
    report_date: str | None
    content: str | None = None

class TimeSeriesData(BaseModel):
    date: datetime
    open: float = Field(..., alias="Open")  # Use alias for DataFrame column names
    high: float = Field(..., alias="High")
    low: float = Field(..., alias="Low")
    close: float = Field(..., alias="Close")
    volume: int = Field(..., alias="Volume")
    adjclose: float = Field(..., alias="AdjClose")  # Use alias for DataFrame column names

class TextRequest(BaseRequest):
    text:str
    language: str = "en"
    max_tokens: int = 2000


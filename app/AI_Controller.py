import threading
import threading
import time
import traceback
import uuid
from datetime import datetime

from openai import RateLimitError

from app import Grok_Controller, OpenAI_Controller
from app.AnalysisResultsStore import AnalysisResultsStore
from app.DocumentStore import DocumentStore
from app.SEC_Controller import SEC_Controller
from app.model import AnalysisRunListResponse, AnalysisRunRequest, AnalysisRunResponse, \
    UploadFileResponse, UploadFileRequest, DocumentRef, \
    PromptData, DocumentContent, AnalysisResult, AnalysisStepResult, TextRequest, BaseResponse

DEFAULT_MODEL = "grok-3-latest"
DEFAULT_TEMPERATURE = 0.01

global_cancel_queued = False
analysis_store = AnalysisResultsStore()
document_store = DocumentStore()
sec_controller = SEC_Controller()


def _check_global_cancel():
    global global_cancel_queued
    if global_cancel_queued:
        raise Exception("Canceled by user")


def _reset_global_cancel():
    global global_cancel_queued
    global_cancel_queued = False


def cancel_queued():
    global global_cancel_queued
    global_cancel_queued = True

    return {'status': 'cancelling', 'message': 'Cancelling queued analyses'}


def _report_exception(owner: str, analysis_id: str, request: AnalysisRunRequest, e: Exception):
    if analysis_id is None:
        analysis_id = analysis_store.create(owner, request, doc_refs=[])

    result = analysis_store.get_result(analysis_id)
    result.error = str(e)
    result.completed_at = datetime.now().astimezone()


def upload_document(owner: str, request: UploadFileRequest) -> UploadFileResponse:
    try:
        document_id = document_store.add_document(request.document)
        return UploadFileResponse(owner=owner, request=request,
                                  document_ref=DocumentRef(type=request.document.type, document_id=document_id))
    except Exception as e:
        traceback.print_exc()
        print("upload_document exception: ", e)
        return UploadFileResponse(owner=owner, request=request, status="ERROR", message=str(e))


def _build_documents_list(prompt: PromptData, tickers: list[str]) -> list[DocumentRef]:
    document_references: list[DocumentRef] = []

    for ticker in tickers:
        # document_store.get_document_list(ticker)
        cik = sec_controller.symbol_store.get_cik(ticker)
        filings = sec_controller.get_company_filings(cik)

        for doc_type in prompt.document_types:
            for filing in filings:
                if filing.form == doc_type.value.upper():
                    document_references.append(
                        DocumentRef(type=doc_type, cik=cik, ticker=ticker, accession_number=filing.accession_number))

    return document_references

def _run_steps(request: AnalysisRunRequest,
               analysis_id: str,
               documents: list[DocumentContent]) -> AnalysisResult:

    prompt = request.prompt
    model = prompt.model or DEFAULT_MODEL

    result: AnalysisResult = AnalysisResult(analysis_id=analysis_id, status='running', prompt=request.prompt)
    analysis_store.update(analysis_id, 'running', result)
    analysis = analysis_store.get_result(analysis_id)

    step_number = 0
    for step_data in prompt.steps:
        _check_global_cancel()
        step_number += 1
        started_at = datetime.now().astimezone()

        # source_format = 'sourceFormat' in step_data and step_data['sourceFormat'] or 'text'
        model_action = step_data.model_action or 'completion'
        response_format = step_data.response_format or 'text'

        print(f"** sending {prompt.name} step {step_number}")

        reply: str = None

        if model.startswith('grok:'):
            match model_action:
                case 'completion':
                    # (reply, response) = Grok_Controller.run_completion_api(prompt, step_data, documents)
                    (reply, _) = Grok_Controller.run_langchain(request, step_data, documents)

        if model.startswith('openai:'):
            match model_action:
                case 'completion':
                    # For completion, use the API-based approach and pass the analysis object
                    (reply, _) = OpenAI_Controller.run_completion_api(prompt, step_data, documents, analysis)
                case 'response':
                    (reply, _) = OpenAI_Controller.run_response_api(prompt, step_data, documents, analysis)
                case 'search':
                    # For search, use the search function
                    (reply, _) = OpenAI_Controller.run_search_api(prompt, step_data, documents)
                case 'summarize':
                    # For summarize, use the summarize function
                    (reply, _) = OpenAI_Controller.run_summarize(prompt, step_data, documents)
                # case 'vectorize':
                #     # For vectorize, use the vectorize function
                #     reply = OpenAI_Controller.run_vectorize(prompt, step_data, documents)

        if not reply:
            raise Exception(f"Empty reply for step {step_number} check model and model_action")

        if result.results is None:
            result.results = []

        result.results.append(AnalysisStepResult(
            step_number=step_number,
            step_data=step_data,
            output=reply,
            response_format=response_format,
            started_at=started_at,
            completed_at=datetime.now().astimezone()
        ))

        analysis_store.update(analysis_id, results=result)

    analysis_store.update(analysis_id, status='completed')
    return result


def _run_in_background(owner: str,
                       request: AnalysisRunRequest,
                       doc_list: list[DocumentRef],
                       analysis_id: str) -> AnalysisResult | None:
    try:
        error_count = 0
        result: AnalysisResult | None = None
        doc_content: list[DocumentContent] = document_store.get_documents(doc_list)

        while error_count < 3:
            _check_global_cancel()

            try:
                result = _run_steps(request, analysis_id, doc_content)
                analysis_store.update(analysis_id, status='completed', results=result)
                return result
            except RateLimitError as e:
                error_count += 1

                if e.code == 'insufficient_quota':
                    raise Exception(f"RateLimitError(_run_steps): {e.message}")

                if error_count >= 3:
                    raise Exception("RateLimitError(_run_steps): Maximum retries exceeded (3)")

                print(f"error: {e}. pausing for 20 seconds...")
                time.sleep(20)

        return result

    except Exception as e:
        traceback.print_exc()
        print("_run_in_background: Exception: ", e)
        _report_exception(owner, analysis_id, request, e)
        analysis_store.update(analysis_id, status='failed', error=str(e))
        return None


def _run_in_background_one_by_one(owner, request: AnalysisRunRequest) -> list[AnalysisResult]:
    results: list[AnalysisResult] = []
    for ticker in _normalize_ticker(request):
        _check_global_cancel()

        request_copy = AnalysisRunRequest(**request.model_dump())

        request_copy.tickers = [ticker]
        doc_list = _build_documents_list(request.prompt, [ticker])
        analysis_id = analysis_store.create(owner, request_copy, doc_list)

        try:
            result = _run_in_background(owner, request_copy, doc_list, analysis_id)
            results.append(result)

        except Exception as e:
            print("_run_in_background_one_by_one: Exception", e)
            _report_exception(owner, analysis_id, request_copy, e)

    return results


def _normalize_ticker(request: AnalysisRunRequest) -> list[str]:
    if isinstance(request.tickers, list):
        return request.tickers

    raise Exception("No tickers found")


def run_prompt_sync(owner: str, request: AnalysisRunRequest) -> AnalysisResult:
    prompt = request.prompt
    analysis_id: str = str(uuid.uuid4())
    doc_list: list[DocumentRef] = []

    final_tickers = _normalize_ticker(request)

    if len(final_tickers) == 0:
        raise Exception("No tickers found")

    analysis_id = analysis_store.create(owner, request, doc_list)

    doc_list = _build_documents_list(prompt, final_tickers)
    result = _run_in_background(owner, request, doc_list, analysis_id)
    return result


def run_prompt(owner: str, request: AnalysisRunRequest) -> AnalysisRunResponse:
    _reset_global_cancel()

    prompt = request.prompt
    analysis_id: str | None = None

    try:
        final_tickers = _normalize_ticker(request)

        if len(final_tickers) == 0:
            raise Exception("No tickers found")

        doc_list = _build_documents_list(prompt, final_tickers)
        analysis_id = analysis_store.create(owner, request, doc_list)
        thread = threading.Thread(target=_run_in_background,
                                  args=(owner, request, doc_list, analysis_id))
        thread.start()

        return AnalysisRunResponse(owner=owner, request=request, analysis_id=analysis_id)

    except Exception as e:
        traceback.print_exc()
        print("run_prompt exception: ", e)
        _report_exception(owner, analysis_id, request, e)

        return AnalysisRunResponse(owner=owner, request=request,
                                   analysis_id=analysis_id,
                                   message=str(e))

def get_analysis_results(analysis_id) -> AnalysisResult:
    result = analysis_store.get_result(analysis_id)
    if result is None:
        raise KeyError(f"Analysis not found for id {analysis_id}")

    return result


def get_analysis_run_list() -> BaseResponse:
    ids = analysis_store.get_analysis_ids()

    data = []
    for id in ids:
        status = analysis_store.get_analysis_status(id)
        data.append((id, status))

    return BaseResponse(data=data)


def summarize(request:TextRequest) -> str:
    return Grok_Controller.summarize(text=request.text, language=request.language, max_tokens= request.max_tokens)


def translate(request:TextRequest) -> str:
    return Grok_Controller.translate(text=request.text, language=request.language, max_tokens= request.max_tokens)

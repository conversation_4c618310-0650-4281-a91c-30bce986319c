import uuid
from datetime import datetime
from typing import Any

import cachetools

from app.model import AnalysisRunRequest, DocumentRef, AnalysisResult


class AnalysisResultsStore:
    def __init__(self):
        self.cache = cachetools.TTLCache(maxsize=250, ttl=3600)

    def _add_analysis(self, analysis_id: str, analysis: AnalysisResult):
        self.cache[analysis_id] = analysis
        pass

    def get_result(self, analysis_id) -> AnalysisResult:
        if analysis_id not in self.cache:
            raise KeyError(f"Analysis not found")

        return self.cache.get(analysis_id)

    def remove_analysis(self, analysis_id: str):
        del self.cache[analysis_id]

    def clear_cache(self):
        self.cache = {}

    def get_analysis_ids(self) -> Any:
        return self.cache.keys()

    def get_analysis_status(self, analysis_id) -> str:
        return self.get_result(analysis_id).status

    def update(self, analysis_id: str, status=None, results=None, error=None):
        if analysis_id not in self.cache:
            raise KeyError(f"Analysis not found", analysis_id)

        analysis: AnalysisResult = self.cache[analysis_id]
        if status:
            analysis.status = status

        if status == 'completed':
            analysis.completed_at = datetime.now().astimezone()

        if error:
            analysis.error = error
            analysis.completed_at = datetime.now().astimezone()

        if results:
            analysis.results = results

    def create(self, owner: str, request: AnalysisRunRequest, doc_refs: list[DocumentRef]) -> str:
        prompt = request.prompt
        # final_ticker_set = request.ticker_set or prompt.ticker_set or None
        final_tickers = request.tickers or prompt.tickers or None

        _id = str(uuid.uuid4())
        self._add_analysis(_id, AnalysisResult(
            analysis_id=_id,
            owner=owner,
            prompt=prompt,
            tickers=final_tickers,
            # 'ticker_set_data': final_ticker_set,
            doc_refs=doc_refs,
            started_at=datetime.now().astimezone(),  # Store as Date object
            status='pending'
        ))
        return _id
